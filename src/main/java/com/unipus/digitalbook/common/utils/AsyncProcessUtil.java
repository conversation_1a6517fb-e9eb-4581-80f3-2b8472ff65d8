package com.unipus.digitalbook.common.utils;

import com.unipus.digitalbook.common.exception.business.BizException;
import com.unipus.digitalbook.model.enums.ResultMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 通用异步处理工具类
 * 提供统一的同步/异步处理机制
 *
 * @param <T> 处理结果类型
 */
@Component
@Slf4j
public class AsyncProcessUtil<T> {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private VirtualThreadPoolManager virtualThreadPoolManager;

    private static final long REDIS_EXPIRE_SECONDS = 300L;
    private static final String REDIS_KEY_PREFIX = "ASYNC_PROCESS:";
    private static final String PROCESS_STATE_RUNNING = "running";
    private static final String PROCESS_STATE_FINISHED = "finished";
    private static final String PROCESS_STATE_ERROR = "error";

    /**
     * 执行处理任务，支持同步或异步模式
     * -
     * 该方法通过 identifier 参数生成唯一的任务标识，用于防止重复提交和记录任务执行状态。
     * 任务状态包括：运行中（running）、已完成（finished）、异常（error），存储于 Redis 中，
     * 键名为 ASYNC_PROCESS:identifier，过期时间为5分钟。
     * -
     * 执行逻辑：
     * 1. 若 async 为 true：任务将提交至虚拟线程池异步执行，立即返回 defaultResult
     * 2. 若 async 为 false：任务在当前线程同步执行，返回实际执行结果
     * -
     * @param async          是否异步执行：true 表示异步，false 表示同步
     * @param identifier     任务唯一标识符，建议使用业务ID或UUID
     * @param task           要执行的具体任务逻辑
     * @param defaultResult  异步模式下的默认返回值
     * @return 同步模式下返回任务执行结果；异步模式下返回 defaultResult
     * @throws BizException 如果任务已在执行中抛出异常（防止重复提交）
     */
    public T process(boolean async, String identifier, Supplier<T> task, T defaultResult) {
        if(isProcessRunning(identifier)){
            log.debug("任务处理中，请稍后再试: {}", identifier);
            throw new BizException(ResultMessage.BIZ_ASYNC_TASK_IS_RUNNING);
        }
        setProcessState(identifier, PROCESS_STATE_RUNNING, null);

        if (async) {
            // 在提交任务的线程中捕获MDC上下文
            final Map<String, String> context = MDC.getCopyOfContextMap();

            // 异步执行 - 使用统一的虚拟线程池管理器
            virtualThreadPoolManager.executeAsync(() -> {
                try {
                    // 在执行任务的线程中设置MDC上下文
                    if (context != null) MDC.setContextMap(context);

                    task.get();
                    setProcessState(identifier, PROCESS_STATE_FINISHED, null);
                } catch (Exception e) {
                    setProcessState(identifier, PROCESS_STATE_ERROR, e);
                    log.error("异步任务执行异常: {}", identifier, e);
                }finally {
                    // 清理MDC上下文，避免影响线程池中的后续任务
                    MDC.clear();
                }
            });

            // 异步模式下返回默认值
            return defaultResult;
        } else {
            // 同步执行
            try {
                var result = task.get();
                setProcessState(identifier, PROCESS_STATE_FINISHED, null);
                return result;
            } catch (Exception e) {
                setProcessState(identifier, PROCESS_STATE_ERROR, e);
                log.error("同步任务执行异常: {}", identifier, e);
                throw e;
            }
        }
    }

    /**
     * 检查处理任务是否正在运行
     *
     * @param identifier 标识符
     * @return true表示正在运行，false表示未运行
     */
    public boolean isProcessRunning(String identifier) {
        ProcessStateInfo stateInfo = getProcessState(identifier);
        return stateInfo != null && PROCESS_STATE_RUNNING.equals(stateInfo.state());
    }

    /**
     * 获取处理任务的状态信息，包括异常信息（如果有）
     *
     * @param identifier 标识符
     * @return 进程状态信息，包含状态和异常信息（如果有）
     */
    public ProcessStateInfo getProcessState(String identifier) {
        String key = getRedisKey(identifier);
        String stateJson = stringRedisTemplate.opsForValue().get(key);
        if (stateJson == null) {
            return null;
        }
        stringRedisTemplate.delete(key);
        var stateInfo = ProcessStateInfo.fromJsonStr(stateJson);
        if(stateInfo.state().equals(PROCESS_STATE_ERROR)){
            throw new BizException(stateInfo.message());
        }
        return stateInfo;
    }

    /**
     * 设置任务处理状态
     */
    private void setProcessState(String identifier, String state, Exception exception) {
        ProcessStateInfo stateInfo = new ProcessStateInfo(state, exception==null ? null : exception.getMessage());
        String stateInfoStr = stateInfo.toJsonStr();
        stringRedisTemplate.opsForValue().set(getRedisKey(identifier), stateInfoStr, REDIS_EXPIRE_SECONDS, TimeUnit.SECONDS);
    }

    private String getRedisKey(String identifier) {
        return REDIS_KEY_PREFIX + identifier;
    }

    /**
     * 进程状态信息
     */
    public record ProcessStateInfo(String state, String message) {

        public String toJsonStr() {
            return JsonUtil.toJsonString(this);
        }

        public static ProcessStateInfo fromJsonStr(String json) {
            return JsonUtil.parseObject(json, ProcessStateInfo.class);
        }
    }

}