package com.unipus.digitalbook.service;

import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoResponse;
import com.unipus.digitalbook.model.entity.cos.COSCredential;
import com.unipus.digitalbook.model.entity.cos.COSMediaTranscodeJob;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeTemplate;

import java.util.Date;
import java.util.List;

/**
 * COS业务服务接口
 *
 * <AUTHOR>
 * @date 2024/12/11
 */
public interface COSService {

    /**
     * 获取当前客户端配置中的region
     *
     * @return region
     */
    String getRegion();

    /**
     * 获取当前客户端配置中的bucket
     *
     * @return bucket
     */
    String getBucket();

    /**
     * 获取当前客户端配置中的appid
     *
     * @return 返回当前凭证中的appid
     */
    String getAppId();

    /**
     * 获取当前客户端配置中的域名
     *
     * @return 域名
     */
    String getDomain();

    /**
     * 获取COS临时密钥
     *
     * @param actions   授权策略
     * @param resources 资源路径
     * @return COSCredential 临时凭证
     */
    COSCredential getCredential(String[] actions, String[] resources);

    /**
     * 根据指定的键获取对象的URL
     *
     * @param key 对象的唯一标识符
     * @return 对象的URL地址
     */
    String getUrlByKey(String key);

    /**
     * 根据URL获取对象键
     *
     * @param url 对象的URL地址
     * @return 对象的键
     */
    String getKeyByUrl(String url);

    /**
     * 根据对象键获取预签名URL
     *
     * @param key            对象的键
     * @param expirationDate 签名的过期时间
     * @return 预签名URL字符串
     */
    String getPresignedUrlByKey(String key, Date expirationDate);

    /**
     * 根据文件URL获取预签名URL
     *
     * @param url            文件URL
     * @param expirationDate 过期时间
     * @return 预签名URL
     */
    String getPresignedUrlByUrl(String url, Date expirationDate);

    /**
     * 获取缓存预签名URL
     *
     * @param url 原始URL
     * @return 预签名URL
     */
    String getCachePresignedUrlByUrl(String url);

    /**
     * 上传文件并获取其URL地址
     *
     * @param fileName 文件名
     * @param bytes    文件内容的字节数组
     * @return 上传文件的URL地址
     */
    String getUploadUrl(String fileName, byte[] bytes);

    /**
     * 上传文本内容并获取其URL地址（公开访问）
     *
     * @param fileName 文件名
     * @param content  文件内容的字符串
     * @return 上传文件的URL地址
     */
    String getUploadContentUrl(String fileName, String content);

    /**
     * 上传文本内容并获取其URL地址（私有访问）
     *
     * @param fileName 文件名
     * @param content  文件内容的字符串
     * @return 上传文件的URL地址
     */
    String getPrivateUploadContentUrl(String fileName, String content);

    /**
     * 上传文件并获取预签名URL
     *
     * @param fileName       文件名
     * @param bytes          文件的字节流
     * @param expirationDate 预签名URL的过期时间
     * @return 预签名URL
     */
    String getUploadPresignedUrl(String fileName, byte[] bytes, Date expirationDate);

    /**
     * 根据URL下载文件内容
     *
     * @param url 对象的URL地址
     * @return 文件内容字符串
     */
    String getDownloadContentByUrl(String url);

    /**
     * 根据URL下载文件内容（带缓存）
     *
     * @param contentUrl     内容URL
     * @param cachePrefix    缓存前缀
     * @param timeoutSeconds 缓存超时时间（秒）
     * @return 内容字符串
     */
    String getCacheDownloadContentByUrl(String contentUrl, String cachePrefix, long timeoutSeconds);

    /**
     * 复制COS对象
     *
     * @param sourceUrl 源文件URL
     * @param targetUrl 目标文件URL
     * @return 目标文件的完整URL
     */
    String copyObject(String sourceUrl, String targetUrl);

    /**
     * 查询音视频转码模板
     *
     * @param name 模板名称
     * @return 模板ID，如果不存在则返回null
     */
    String getMediaTranscodeTemplate(String name);

    /**
     * 查询音视频转码模板（带缓存）
     *
     * @param name 模板名称
     * @return 模板ID，如果不存在则返回null
     */
    String getCachedMediaTranscodeTemplate(String name);

    /**
     * 查询所有音视频转码模板
     *
     * @return 模板列表
     */
    List<MediaTranscodeTemplate> getAllMediaTranscodeTemplates();

    /**
     * 删除音视频转码模板
     *
     * @param templateId 模板ID
     * @return 删除是否成功
     */
    Boolean deleteMediaTranscodeTemplate(String templateId);

    /**
     * 创建音视频转码任务
     *
     * @param inputObject  输入文件对象键或URL
     * @param outputObject 输出文件对象键或URL
     * @param templateId   转码模板ID
     * @return 任务ID
     */
    String createMediaTranscodeJob(String inputObject, String outputObject, String templateId);

    /**
     * 创建并标记音频转码任务
     *
     * @param templateName 转码模板名称
     * @param inputUrl 输入文件URL
     * @param hash 文件hash值
     * @return 任务ID
     */
    String createAndMarkMediaTranscodeJob(String templateName, String inputUrl, String hash);

    /**
     * 查询媒体转码任务详情
     *
     * @param jobId 任务ID
     * @return 任务详情对象
     */
    COSMediaTranscodeJob getMediaTranscodeJob(String jobId);

    /**
     * 取消音视频转码任务
     *
     * @param jobId 任务ID
     * @return 取消是否成功
     */
    Boolean cancelMediaTranscodeJob(String jobId);

    /**
     * 标记文件为已转码
     *
     * @param templateName 模板名称
     * @param url          文件URL
     * @param hash         文件hash值
     */
    void markMediaTranscodeJob(String templateName, String url, String hash);

    /**
     * 检查文件是否已转码并返回文件路径
     *
     * @param templateName 模板名称
     * @param url          文件URL
     * @param hash         文件hash值
     * @return 如果已转码返回文件路径，否则返回null
     */
    String checkMediaTranscodeJob(String templateName, String url, String hash);

    /**
     * 检查COS对象是否存在
     *
     * @param key 对象键
     * @return 是否存在
     */
    boolean doesObjectExist(String key);

    /**
     * 获取媒体信息
     *
     * @param inputObjectKey 输入文件对象键
     * @return 媒体信息
     */
    MediaInfoResponse getMediaInfo(String inputObjectKey);

    /**
     * 获取媒体信息（带缓存）
     *
     * @param inputObjectKey 输入文件对象键
     * @return 媒体信息
     */
    MediaInfoResponse getCachedMediaInfo(String inputObjectKey);

    /**
     * 创建音视频转码任务
     *
     * @param inputObjectKey 输入文件对象键或URL
     * @param format         转码格式
     * @param hash           文件hash值
     * @return 任务ID
     */
    String createMediaTranscodeFormatJob(String inputObjectKey, String format, String hash);

    /**
     * 创建视频转码任务
     *
     * @param url  输入文件URL
     * @param hash 文件hash值
     */
    void createVideoMediaTranscodeJob(String url, String hash);
}
