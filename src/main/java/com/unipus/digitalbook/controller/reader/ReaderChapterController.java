package com.unipus.digitalbook.controller.reader;

import com.unipus.digitalbook.controller.BaseController;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.assistant.AssistantPositionInfoListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterCatalogListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterResourceListDTO;
import com.unipus.digitalbook.model.dto.chapter.ChapterWithContentDTO;
import com.unipus.digitalbook.model.dto.question.BigQuestionGroupListDTO;
import com.unipus.digitalbook.model.entity.action.NodeCompletion;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.chapter.ChapterVersion;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.enums.ContentTypeEnum;
import com.unipus.digitalbook.model.enums.ReaderTypeEnum;
import com.unipus.digitalbook.model.params.action.NodeDTO;
import com.unipus.digitalbook.model.params.action.NodeListDTO;
import com.unipus.digitalbook.service.AssistantService;
import com.unipus.digitalbook.service.BookVersionService;
import com.unipus.digitalbook.service.ChapterService;
import com.unipus.digitalbook.service.QuestionService;
import com.unipus.digitalbook.service.useraction.strategy.content.UserContentActionFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("reader/chapter")
@Tag(name = "读者学习教材章节相关接口", description = "读者学习教材章节相关接口")
public class ReaderChapterController extends BaseController {
    @Resource
    private ChapterService chapterService;

    @Resource
    private BookVersionService bookVersionService;

    @Resource
    private QuestionService questionService;

    @Resource
    private AssistantService assistantService;

    @Resource
    private UserContentActionFactory userContentActionFactory;

    @GetMapping("/getChapterList")
    @Tag(name = "获取教材章节列表", description = "获取教材章节列表")
    public Response<ChapterListDTO> getChapterList(String bookId, String bookVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        BookVersion bookVersionByBookIdAndVersion = bookVersionService.getBookVersionByBookIdAndVersion(bookId, bookVersionNumber);
        if (bookVersionByBookIdAndVersion == null || bookVersionByBookIdAndVersion.getId() == null) {
            return Response.fail("教材版本不存在:章节列表");
        }
        Long bookVersionId = bookVersionByBookIdAndVersion.getId();
        List<Chapter> chapters = chapterService.getChapterNamesByBookVersionId(bookVersionId);
        if (CollectionUtils.isEmpty(chapters)) {
            return Response.success(new ChapterListDTO());
        }
        return Response.success(new ChapterListDTO(chapters));
    }

    @GetMapping("/getChapterInfo")
    @Tag(name = "获取教材章节信息", description = "获取教材章节信息")
    public Response<ChapterWithContentDTO> getChapterInfo(String bookId, String bookVersionNumber, String chapterId) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        BookVersion bookVersionByBookIdAndVersion = bookVersionService.getBookVersionByBookIdAndVersion(bookId, bookVersionNumber);
        if (bookVersionByBookIdAndVersion == null || bookVersionByBookIdAndVersion.getId() == null) {
            return Response.fail("教材版本不存在: 章节信息");
        }
        Long bookVersionId = bookVersionByBookIdAndVersion.getId();
        Chapter chapter = chapterService.getChapterByBookVersionIdAndChapterId(bookVersionId, chapterId);
        if (chapter == null) {
            return Response.fail("章节内容不存在");
        }
        ChapterWithContentDTO chapterWithContent = new ChapterWithContentDTO();
        chapterWithContent.fillChapterInfo(chapter);
        chapterWithContent.fillVersionInfoWithReaderType(chapter.getChapterVersion(), readerType);
        return Response.success(chapterWithContent);
    }

    @GetMapping("/getBookResource")
    @Tag(name = "获取教材资源信息", description = "获取教材资源信息")
    public Response<ChapterResourceListDTO> getBookResource(String bookId, String bookVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        List<ChapterVersion> chapterVersions = chapterService.getResourceByBookIdAndVersion(bookId, bookVersionNumber);
        return Response.success(new ChapterResourceListDTO(bookId, chapterVersions));
    }
    @GetMapping("/getBookCatalog")
    @Tag(name = "获取教材的目录列表", description = "获取教材的目录列表")
    public Response<ChapterCatalogListDTO> getBookCatalog(String bookId, String bookVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        List<ChapterVersion> catalogByBookVersionId = chapterService.getCatalogByBookIdAndVersion(bookId, bookVersionNumber);
        return Response.success(new ChapterCatalogListDTO(bookId, catalogByBookVersionId));
    }
    @GetMapping("/getQuestions")
    @Tag(name = "获取教材的题目列表", description = "获取教材的题目列表")
    public Response<BigQuestionGroupListDTO> getQuestions(String chapterId, String chapterVersionNumber) {
        ReaderTypeEnum readerType = getReaderType();
        if (readerType == null) {
            return Response.fail("用户信息校验失败");
        }
        List<Long> questionIds = chapterService.getQuestionIds(chapterId, chapterVersionNumber);
        List<BigQuestionGroup> questions = questionService.batchGetBigQuestions(questionIds);
        return Response.success(new BigQuestionGroupListDTO(questions, readerType == ReaderTypeEnum.TEACHER));
    }

    @GetMapping("/latestList")
    @Operation(summary = "获取最新数字人列表",
            description = "获取最新数字人列表",
            method = "GET"
    )
    public Response<AssistantPositionInfoListDTO> latestList(@RequestParam("bookId") String bookId,
                                                             @RequestParam("bookVersionNumber") String bookVersionNumber,
                                                             @RequestParam("chapterId") String chapterId) {

        return assistantService.latestInstanceList(getOpenId(), bookId, bookVersionNumber, chapterId);

    }

    @GetMapping("/getChapterNodeList")
    @Operation(summary = "获取章节的节点", description = "获取章节的节点", method = "GET")
    public Response<NodeListDTO> getChapterNodeList(String chapterId, String versionNumber) {
        Long chapterVersionId = chapterService.getChapterVersionIdByChapterIdAndVersionNumber(chapterId, versionNumber);
        if (chapterVersionId == null) {
            return Response.fail("章节版本不存在");
        }
        Response<NodeCompletion> nodeListResponse = userContentActionFactory.
                getContentAction(ContentTypeEnum.CHAPTER)
                .getFinishedNodeList(getTenantId(), getEnvPartition(), getOpenId(), chapterId, chapterVersionId);
        if (!nodeListResponse.isSuccess()) {
            return null;
        }
        Map<String, Integer> nodeCompletedCount = nodeListResponse.getData().getNodeCompletedCount();
        Collection<ChapterNode> chapterNodes = chapterService.getNodeStructureCache(chapterVersionId).getNodeMap().values();
        if (nodeCompletedCount == null || nodeCompletedCount.isEmpty()) {
            return Response.success(new NodeListDTO(chapterNodes));
        }
        return Response.success(new NodeListDTO(chapterNodes, nodeCompletedCount));
    }
}
