package com.unipus.digitalbook.controller;

import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.cos.COSCredentialDTO;
import com.unipus.digitalbook.model.dto.cos.COSMediaTranscodeJobDTO;
import com.unipus.digitalbook.model.dto.cos.MediaTranscodeTemplateListDTO;
import com.unipus.digitalbook.model.entity.cos.COSMediaTranscodeJob;
import com.unipus.digitalbook.model.entity.cos.MediaTranscodeTemplate;
import com.unipus.digitalbook.model.params.cos.CheckMediaTranscodeJobParam;
import com.unipus.digitalbook.model.params.cos.CreateMediaTranscodeJobParam;
import com.unipus.digitalbook.model.params.cos.CreateVideoMediaTranscodeJobParam;
import com.unipus.digitalbook.model.params.cos.DeleteMediaTranscodeTemplateParam;
import com.unipus.digitalbook.service.COSService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/cos")
@Tag(name = "COS相关功能", description = "COS相关接口")
public class COSController extends BaseController {

    @Resource
    private COSService cosService;

    @GetMapping("getCredential")
    @Operation(summary = "临时密钥", description = "临时密钥", method = "GET")
    public Response<COSCredentialDTO> getCredential() {
        String[] actions = {
                //简单上传操作
                "name/cos:PutObject",
                //表单上传对象
                "name/cos:PostObject",
                //分块上传：初始化分块操作
                "name/cos:InitiateMultipartUpload",
                //分块上传：List 进行中的分块上传
                "name/cos:ListMultipartUploads",
                //分块上传：List 已上传分块操作
                "name/cos:ListParts",
                //分块上传：上传分块操作
                "name/cos:UploadPart",
                //分块上传：完成所有分块上传操作
                "name/cos:CompleteMultipartUpload",
                //取消分块上传操作
                "name/cos:AbortMultipartUpload"
        };
        return Response.success(new COSCredentialDTO(cosService.getCredential(actions, null)));
    }

    @GetMapping("getCOSCredential")
    @Operation(summary = "临时密钥", description = "临时密钥", method = "GET")
    public Response<COSCredentialDTO> getCOSCredential() {
        String[] actions = {
                //简单上传操作
                "name/cos:PutObject",
                //表单上传对象
                "name/cos:PostObject",
                //分块上传：初始化分块操作
                "name/cos:InitiateMultipartUpload",
                //分块上传：List 进行中的分块上传
                "name/cos:ListMultipartUploads",
                //分块上传：List 已上传分块操作
                "name/cos:ListParts",
                //分块上传：上传分块操作
                "name/cos:UploadPart",
                //分块上传：完成所有分块上传操作
                "name/cos:CompleteMultipartUpload",
                //取消分块上传操作
                "name/cos:AbortMultipartUpload"
        };
        String identifier = getCurrentUserId() != null ? getCurrentUserId().toString() : getOpenId();
        String pathPrefix = DigestUtils.md5Hex(identifier) + "/" + UUID.randomUUID() + "/";

        String resource = String.format("qcs::cos:%s:uid/%s:%s/%s",
                cosService.getRegion(),
                cosService.getAppId(),
                cosService.getBucket(),
                pathPrefix + "*");
        String[] resources = new String[]{resource};
        COSCredentialDTO cosCredentialDTO = new COSCredentialDTO(cosService.getCredential(actions, resources));
        cosCredentialDTO.setPathPrefix(pathPrefix);
        cosCredentialDTO.setDomain(cosService.getDomain());
        return Response.success(cosCredentialDTO);
    }

    /**
     * 获取作答临时密钥
     * <p>
     * 限制文件大小
     * https://cloud.tencent.com/document/product/436/118303
     * https://cloud.tencent.com/document/product/436/14048
     * https://github.com/tencentyun/qcloud-cos-sts-sdk/blob/e0eec8a5d123544e4a5b3e714f70adacb12f06b6/java/src/test/java/com/tencent/cloud/GetKeyAndCredentialsTest.java#L42
     *
     * @return
     */
    @GetMapping("/answer/getCOSCredential")
    @Operation(summary = "获取作答临时密钥", description = "获取作答临时密钥", method = "GET")
    public Response<COSCredentialDTO> getAnswerCOSCredential() {
        String[] actions = {
                //简单上传操作
                "name/cos:PutObject",
                //表单上传对象
                "name/cos:PostObject",
                //分块上传：初始化分块操作
                "name/cos:InitiateMultipartUpload",
                //分块上传：List 进行中的分块上传
                "name/cos:ListMultipartUploads",
                //分块上传：List 已上传分块操作
                "name/cos:ListParts",
                //分块上传：上传分块操作
                "name/cos:UploadPart",
                //分块上传：完成所有分块上传操作
                "name/cos:CompleteMultipartUpload",
                //取消分块上传操作
                "name/cos:AbortMultipartUpload"
        };
        String identifier = getCurrentUserId() != null ? getCurrentUserId().toString() : getOpenId();
        String pathPrefix = "answer/" + DigestUtils.md5Hex(identifier) + "/" + UUID.randomUUID() + "/";

        String resource = String.format("qcs::cos:%s:uid/%s:%s/%s",
                cosService.getRegion(),
                cosService.getAppId(),
                cosService.getBucket(),
                pathPrefix + "*");
        String[] resources = new String[]{resource};
        COSCredentialDTO cosCredentialDTO = new COSCredentialDTO(cosService.getCredential(actions, resources));
        cosCredentialDTO.setPathPrefix(pathPrefix);
        cosCredentialDTO.setDomain(cosService.getDomain());
        return Response.success(cosCredentialDTO);
    }

    @GetMapping("getCICredential")
    @Operation(summary = "数据万象临时密钥", description = "数据万象临时密钥", method = "GET")
    public Response<COSCredentialDTO> getCICredential() {
        String[] actions = {
                // 处理相关接口一般为数据万象产品 权限中以ci开头
                // 提交文档处理任务
                "ci:CreateDocProcessJobs"
        };
        return Response.success(new COSCredentialDTO(cosService.getCredential(actions, null)));
    }

    @GetMapping("getPresignedUrlByUrl")
    @Operation(summary = "根据URL获取对应文件的预签名URL", description = "根据URL获取对应文件的预签名URL", method = "GET")
    public Response<String> getPresignedUrlByUrl(@RequestParam String url) {
        return Response.success(cosService.getPresignedUrlByUrl(url, null));
    }

    @GetMapping("getDownloadContentByUrl")
    @Operation(summary = "根据URL获取对应文件的文本内容", description = "根据URL获取对应文件的文本内容", method = "GET")
    public Response<String> getDownloadContentByUrl(@RequestParam String url) {
        return Response.success(cosService.getDownloadContentByUrl(url));
    }

    @PostMapping("checkMediaTranscodeJob")
    @Operation(summary = "根据hash值检查文件是否已转码", description = "根据hash值检查文件是否已转码")
    public Response<String> checkMediaTranscodeJob(@RequestBody CheckMediaTranscodeJobParam param) {
        String filePath = cosService.checkMediaTranscodeJob(param.getTemplateName(), param.getUrl(), param.getHash());
        return Response.success(filePath);
    }

    @PostMapping("createMediaTranscodeJob")
    @Operation(summary = "创建音频转码任务", description = "创建音频转码任务")
    public Response<String> createMediaTranscodeJob(@RequestBody CreateMediaTranscodeJobParam param) {
        // 创建转码任务,如果提供了hash值，标记为已转码
        String jobId = cosService.createAndMarkMediaTranscodeJob(param.getTemplateName(), param.getUrl(), param.getHash());
        return Response.success(jobId);
    }

    @GetMapping("getMediaTranscodeJob")
    @Operation(summary = "查询音视频转码任务", description = "查询音视频转码任务")
    public Response<COSMediaTranscodeJobDTO> getMediaTranscodeJob(
            @Schema(description = "任务ID") @RequestParam String jobId) {
        COSMediaTranscodeJob job = cosService.getMediaTranscodeJob(jobId);
        return Response.success(new COSMediaTranscodeJobDTO(job));
    }

    @GetMapping("cancelMediaTranscodeJob")
    @Operation(summary = "取消音视频转码任务", description = "取消音视频转码任务")
    public Response<Boolean> cancelMediaTranscodeJob(
            @Schema(description = "任务ID") @RequestParam String jobId) {
        Boolean result = cosService.cancelMediaTranscodeJob(jobId);
        return Response.success(result);
    }

    @GetMapping("getAllMediaTranscodeTemplates")
    @Operation(summary = "查询所有音频转码模版", description = "查询所有音频转码模版")
    public Response<MediaTranscodeTemplateListDTO> getAllMediaTranscodeTemplates() {
        List<MediaTranscodeTemplate> templates = cosService.getAllMediaTranscodeTemplates();
        return Response.success(new MediaTranscodeTemplateListDTO(templates));
    }

    @PostMapping("deleteMediaTranscodeTemplate")
    @Operation(summary = "删除音频转码模版", description = "删除音频转码模版")
    public Response<Boolean> deleteMediaTranscodeTemplate(@RequestBody DeleteMediaTranscodeTemplateParam param) {
        Boolean result = cosService.deleteMediaTranscodeTemplate(param.getTemplateId());
        return Response.success(result);
    }

    @PostMapping("createVideoMediaTranscodeJob")
    @Operation(summary = "创建视频转码任务", description = "创建视频转码任务")
    public Response<Boolean> createVideoMediaTranscodeJob(@RequestBody CreateVideoMediaTranscodeJobParam param) {
        cosService.createVideoMediaTranscodeJob(param.getUrl(), param.getHash());
        return Response.success(true);
    }
}
