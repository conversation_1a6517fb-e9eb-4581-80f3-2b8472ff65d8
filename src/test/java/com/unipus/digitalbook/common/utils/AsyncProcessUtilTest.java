package com.unipus.digitalbook.common.utils;

import jakarta.annotation.Resource;
import jodd.util.ThreadUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AsyncProcessUtil 异常缓存功能测试
 */
@SpringBootTest
class AsyncProcessUtilTest {

    @Resource
    private AsyncProcessUtil<String> asyncProcessUtil;

    @Test
    void testAsyncExceptionCaching() throws InterruptedException {
        String identifier = "test-async-exception-" + System.currentTimeMillis();
        String message = "异步异常消息";
        
        // 启动一个会抛出异常的异步任务
        String result = asyncProcessUtil.process(true, identifier, () -> {
            throw new RuntimeException(message);
        }, "默认值");
        
        assertEquals("默认值", result);
        
        // 等待异步任务完成
        Thread.sleep(1000);
        
        // 获取状态信息
        try {
            asyncProcessUtil.getProcessState(identifier);
        } catch (Exception e) {
            assertEquals(message, e.getMessage());
        }
    }

    @Test
    void testSyncExceptionCaching() {
        String identifier = "test-sync-exception-" + System.currentTimeMillis();
        String message = "同步异常消息";
        
        // 启动一个会抛出异常的同步任务
        assertThrows(IllegalArgumentException.class, () -> {
            asyncProcessUtil.process(false, identifier, () -> {
                throw new IllegalArgumentException(message);
            }, null);
        });
        
        // 获取状态信息
        try {
            asyncProcessUtil.getProcessState(identifier);
        } catch (Exception e) {
            assertEquals(message, e.getMessage());
        }
    }

    @Test
    void testRunningTaskState() {
        String identifier = "test-running-" + System.currentTimeMillis();

        // 启动一个长时间运行的异步任务
        asyncProcessUtil.process(true, identifier, () -> {
            ThreadUtil.sleep(5000);
            return "完成";
        }, "默认值");
        
        // 验证 isProcessRunning 方法
        assertTrue(asyncProcessUtil.isProcessRunning(identifier));
    }

    @Test
    void testNonExistentTask() {
        String identifier = "non-existent-task";
        
        // 获取不存在的任务状态
        AsyncProcessUtil.ProcessStateInfo stateInfo = asyncProcessUtil.getProcessState(identifier);
        
        assertNull(stateInfo);
        assertFalse(asyncProcessUtil.isProcessRunning(identifier));
    }
}
